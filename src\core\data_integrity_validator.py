"""
Data Integrity Validator for NSE symbols and OHLCV data.
Validates data consistency across raw tables, symbol mapping, and OHLCV tables.
"""

import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import psycopg2
from psycopg2.extras import RealDictCursor

from ..database.connection import engine
from ..core.config import settings

logger = logging.getLogger(__name__)


class DataIntegrityValidator:
    """Validates data integrity across NSE symbol processing pipeline."""
    
    def __init__(self):
        """Initialize the data integrity validator."""
        self.db_engine = engine
        self.validation_results = {
            'raw_data_integrity': {},
            'symbol_mapping_integrity': {},
            'ohlcv_table_integrity': {},
            'cross_table_consistency': {},
            'summary': {}
        }
    
    def validate_all(self) -> Dict:
        """Run comprehensive data integrity validation."""
        logger.info("Starting comprehensive data integrity validation...")
        
        try:
            # 1. Validate raw data tables
            self._validate_raw_data_integrity()
            
            # 2. Validate symbol mapping table
            self._validate_symbol_mapping_integrity()
            
            # 3. Validate OHLCV table structures
            self._validate_ohlcv_table_integrity()
            
            # 4. Validate cross-table consistency
            self._validate_cross_table_consistency()
            
            # 5. Generate summary
            self._generate_validation_summary()
            
            logger.info("Data integrity validation completed")
            return self.validation_results
            
        except Exception as e:
            logger.error(f"Error during data integrity validation: {e}")
            self.validation_results['error'] = str(e)
            return self.validation_results
    
    def _validate_raw_data_integrity(self) -> None:
        """Validate integrity of raw NSE data tables."""
        logger.info("Validating raw data integrity...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check NSE_CM raw data
                cursor.execute("SELECT COUNT(*) as total_rows FROM nse_cm_raw")
                cm_count = cursor.fetchone()['total_rows']
                
                cursor.execute("SELECT COUNT(DISTINCT symbol_name) as unique_symbols FROM nse_cm_raw")
                cm_unique = cursor.fetchone()['unique_symbols']
                
                # Check NSE_FO raw data
                cursor.execute("SELECT COUNT(*) as total_rows FROM nse_fo_raw")
                fo_count = cursor.fetchone()['total_rows']
                
                cursor.execute("SELECT COUNT(DISTINCT symbol_name) as unique_symbols FROM nse_fo_raw")
                fo_unique = cursor.fetchone()['unique_symbols']
                
                # Check for null/invalid data
                cursor.execute("""
                    SELECT COUNT(*) as null_symbols 
                    FROM nse_cm_raw 
                    WHERE symbol_name IS NULL OR symbol_name = ''
                """)
                cm_null_symbols = cursor.fetchone()['null_symbols']
                
                cursor.execute("""
                    SELECT COUNT(*) as null_symbols 
                    FROM nse_fo_raw 
                    WHERE symbol_name IS NULL OR symbol_name = ''
                """)
                fo_null_symbols = cursor.fetchone()['null_symbols']
                
                self.validation_results['raw_data_integrity'] = {
                    'nse_cm_raw': {
                        'total_rows': cm_count,
                        'unique_symbols': cm_unique,
                        'null_symbols': cm_null_symbols,
                        'data_quality': 'GOOD' if cm_null_symbols == 0 else 'ISSUES'
                    },
                    'nse_fo_raw': {
                        'total_rows': fo_count,
                        'unique_symbols': fo_unique,
                        'null_symbols': fo_null_symbols,
                        'data_quality': 'GOOD' if fo_null_symbols == 0 else 'ISSUES'
                    }
                }
                
            conn.close()
            logger.info("Raw data integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating raw data integrity: {e}")
            self.validation_results['raw_data_integrity']['error'] = str(e)
    
    def _validate_symbol_mapping_integrity(self) -> None:
        """Validate integrity of symbol mapping table."""
        logger.info("Validating symbol mapping integrity...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Total symbols in mapping
                cursor.execute("SELECT COUNT(*) as total_symbols FROM symbol_mapping")
                total_symbols = cursor.fetchone()['total_symbols']
                
                # Symbols by market type
                cursor.execute("""
                    SELECT market_type, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY market_type
                """)
                market_type_counts = {row['market_type']: row['count'] for row in cursor.fetchall()}
                
                # Active vs inactive symbols
                cursor.execute("""
                    SELECT is_active, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY is_active
                """)
                active_counts = {row['is_active']: row['count'] for row in cursor.fetchall()}
                
                # Check for duplicate NSE symbols
                cursor.execute("""
                    SELECT nse_symbol, COUNT(*) as count 
                    FROM symbol_mapping 
                    GROUP BY nse_symbol 
                    HAVING COUNT(*) > 1
                """)
                duplicate_nse = cursor.fetchall()
                
                # Check for duplicate Fyers symbols
                cursor.execute("""
                    SELECT fyers_symbol, COUNT(*) as count 
                    FROM symbol_mapping 
                    WHERE fyers_symbol IS NOT NULL
                    GROUP BY fyers_symbol 
                    HAVING COUNT(*) > 1
                """)
                duplicate_fyers = cursor.fetchall()
                
                self.validation_results['symbol_mapping_integrity'] = {
                    'total_symbols': total_symbols,
                    'market_type_distribution': market_type_counts,
                    'active_distribution': active_counts,
                    'duplicate_nse_symbols': len(duplicate_nse),
                    'duplicate_fyers_symbols': len(duplicate_fyers),
                    'data_quality': 'GOOD' if len(duplicate_nse) == 0 and len(duplicate_fyers) == 0 else 'ISSUES'
                }
                
            conn.close()
            logger.info("Symbol mapping integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating symbol mapping integrity: {e}")
            self.validation_results['symbol_mapping_integrity']['error'] = str(e)
    
    def _validate_ohlcv_table_integrity(self) -> None:
        """Validate integrity of OHLCV tables structure and data."""
        logger.info("Validating OHLCV table integrity...")
        
        ohlcv_tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for table in ohlcv_tables:
                    # Check if table exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = %s
                        )
                    """, (table,))
                    table_exists = cursor.fetchone()['exists']
                    
                    if not table_exists:
                        self.validation_results['ohlcv_table_integrity'][table] = {
                            'exists': False,
                            'error': 'Table does not exist'
                        }
                        continue
                    
                    # Check if fyers_symbol column exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.columns 
                            WHERE table_name = %s AND column_name = 'fyers_symbol'
                        )
                    """, (table,))
                    fyers_column_exists = cursor.fetchone()['exists']
                    
                    # Check if it's a TimescaleDB hypertable
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM timescaledb_information.hypertables 
                            WHERE hypertable_name = %s
                        )
                    """, (table,))
                    is_hypertable = cursor.fetchone()['exists']
                    
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) as row_count FROM {table}")
                    row_count = cursor.fetchone()['row_count']
                    
                    self.validation_results['ohlcv_table_integrity'][table] = {
                        'exists': True,
                        'fyers_column_exists': fyers_column_exists,
                        'is_hypertable': is_hypertable,
                        'row_count': row_count,
                        'status': 'GOOD' if fyers_column_exists and is_hypertable else 'NEEDS_UPDATE'
                    }
                
            conn.close()
            logger.info("OHLCV table integrity validation completed")
            
        except Exception as e:
            logger.error(f"Error validating OHLCV table integrity: {e}")
            self.validation_results['ohlcv_table_integrity']['error'] = str(e)
    
    def _validate_cross_table_consistency(self) -> None:
        """Validate consistency across different tables."""
        logger.info("Validating cross-table consistency...")
        
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check symbols in mapping vs raw tables
                cursor.execute("""
                    SELECT COUNT(DISTINCT nse_symbol) as mapped_symbols 
                    FROM symbol_mapping
                """)
                mapped_symbols = cursor.fetchone()['mapped_symbols']
                
                cursor.execute("""
                    SELECT COUNT(DISTINCT SPLIT_PART(symbol_name, ':', -1)) as raw_cm_symbols 
                    FROM nse_cm_raw
                """)
                raw_cm_symbols = cursor.fetchone()['raw_cm_symbols']
                
                cursor.execute("""
                    SELECT COUNT(DISTINCT SPLIT_PART(symbol_name, ':', -1)) as raw_fo_symbols 
                    FROM nse_fo_raw
                """)
                raw_fo_symbols = cursor.fetchone()['raw_fo_symbols']
                
                total_raw_symbols = raw_cm_symbols + raw_fo_symbols
                
                self.validation_results['cross_table_consistency'] = {
                    'mapped_symbols': mapped_symbols,
                    'raw_cm_symbols': raw_cm_symbols,
                    'raw_fo_symbols': raw_fo_symbols,
                    'total_raw_symbols': total_raw_symbols,
                    'mapping_coverage': round((mapped_symbols / total_raw_symbols * 100), 2) if total_raw_symbols > 0 else 0,
                    'consistency_status': 'GOOD' if mapped_symbols >= total_raw_symbols * 0.95 else 'ISSUES'
                }
                
            conn.close()
            logger.info("Cross-table consistency validation completed")
            
        except Exception as e:
            logger.error(f"Error validating cross-table consistency: {e}")
            self.validation_results['cross_table_consistency']['error'] = str(e)
    
    def _generate_validation_summary(self) -> None:
        """Generate overall validation summary."""
        logger.info("Generating validation summary...")
        
        issues = []
        recommendations = []
        
        # Check raw data issues
        raw_data = self.validation_results.get('raw_data_integrity', {})
        if raw_data.get('nse_cm_raw', {}).get('data_quality') == 'ISSUES':
            issues.append("NSE_CM raw data has null/invalid symbols")
            recommendations.append("Re-run NSE symbol processing with error handling")
        
        if raw_data.get('nse_fo_raw', {}).get('data_quality') == 'ISSUES':
            issues.append("NSE_FO raw data has null/invalid symbols")
            recommendations.append("Re-run NSE symbol processing with error handling")
        
        # Check symbol mapping issues
        mapping_data = self.validation_results.get('symbol_mapping_integrity', {})
        if mapping_data.get('data_quality') == 'ISSUES':
            issues.append("Symbol mapping has duplicate entries")
            recommendations.append("Clean up duplicate symbol mappings")
        
        # Check OHLCV table issues
        ohlcv_data = self.validation_results.get('ohlcv_table_integrity', {})
        for table, info in ohlcv_data.items():
            if isinstance(info, dict) and info.get('status') == 'NEEDS_UPDATE':
                if not info.get('fyers_column_exists'):
                    issues.append(f"{table} missing fyers_symbol column")
                    recommendations.append(f"Run migration to add fyers_symbol column to {table}")
                if not info.get('is_hypertable'):
                    issues.append(f"{table} is not a TimescaleDB hypertable")
                    recommendations.append(f"Convert {table} to TimescaleDB hypertable")
        
        # Check consistency issues
        consistency_data = self.validation_results.get('cross_table_consistency', {})
        if consistency_data.get('consistency_status') == 'ISSUES':
            issues.append("Low symbol mapping coverage")
            recommendations.append("Re-run symbol processing to improve mapping coverage")
        
        self.validation_results['summary'] = {
            'total_issues': len(issues),
            'issues': issues,
            'recommendations': recommendations,
            'overall_status': 'HEALTHY' if len(issues) == 0 else 'NEEDS_ATTENTION',
            'validation_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Validation summary: {len(issues)} issues found")
